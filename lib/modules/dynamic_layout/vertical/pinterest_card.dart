import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../common/tools.dart';
import '../../../models/index.dart' show CartModel, Product;
import '../../../services/service_config.dart';
import '../../../services/services.dart';
import '../../../widgets/common/start_rating.dart';
import '../../../widgets/product/action_button_mixin.dart';
import '../../../widgets/product/index.dart';
import '../config/product_config.dart';

class PinterestCard extends StatelessWidget with ActionButtonMixin {
  final Product item;
  final width;
  final marginRight;
  final kSize size;
  final ProductConfig config;

  const PinterestCard(
      {required this.item,
      this.width,
      this.size = kSize.medium,
      this.marginRight = 10.0,
      required this.config});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currency = Provider.of<CartModel>(context).currencyCode;
    final currencyRates = Provider.of<CartModel>(context).currencyRates;
    final isTablet = MediaQuery.sizeOf(context).width > 600;

    // Helper.isTablet(MediaQuery.of(context));

    var titleFontSize = isTablet ? 24.0 : 14.0;
    var starSize = isTablet ? 20.0 : 10.0;
    var showCart =
        config.showCartIcon && Services().widget.enableShoppingCart(item);

    var isSale = (item.onSale ?? false) &&
        PriceTools.getPriceProductValue(item, onSale: true) !=
            PriceTools.getPriceProductValue(item, onSale: false);

    var priceProduct = PriceTools.getPriceProduct(item, currencyRates, currency,
        onSale: isSale)!;

    return GestureDetector(
      onTap: () => onTapProduct(context, product: item, config: config),
      child: Container(
        color: Colors.transparent,
        // Theme.of(context).cardColor,
        child: Stack(
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Card(
                  color: const Color(0x00000000),
                  elevation: 3.0,
                  child: Container(
                    decoration: BoxDecoration(
                      image: item.imageFeature != null
                          ? DecorationImage(
                              image: NetworkImage(item.imageFeature!),
                              fit: BoxFit.cover,
                            )
                          : null,
                      borderRadius: BorderRadius.all(Radius.circular(10.0)),
                      color:
                          item.imageFeature == null ? Colors.grey[200] : null,
                    ),
                    child: item.imageFeature == null
                        ? Center(child: Icon(Icons.image, color: Colors.grey))
                        : null,
                  ),
                ),
                if (!config.showOnlyImage)
                  Container(
                    alignment: Alignment.topLeft,
                    padding: const EdgeInsets.only(
                      top: 10,
                      left: 8,
                      right: 8,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        if (!config.hideTitle) ...[
                          Text(item.name!,
                              style: TextStyle(
                                fontSize: titleFontSize,
                              ),
                              maxLines: 1),
                          const SizedBox(height: 6),
                        ],
                        StoreName(product: item, hide: config.hideStore),
                        item.tagLine != null
                            ? Text(
                                item.tagLine.toString(),
                                maxLines: 1,
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontStyle: FontStyle.italic,
                                ),
                              )
                            : const SizedBox(),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ProductPricing(
                              product: item,
                              hide: config.hidePrice,
                              fromWebLayout: false,
                            ),
                            // if (!config.hidePrice) ...[
                            //   const SizedBox(height: 6),
                            //   Text(priceProduct,
                            //       style: TextStyle(
                            //           color: theme.colorScheme.secondary)),
                            // ],
                            const SizedBox(
                              height: 4,
                            ),
                            if (config.showStockStatus)
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  StockStatus(product: item, config: config),
                                  if (showCart &&
                                      !item.isEmptyProduct() &&
                                      !ServerConfig().isListingType) ...[
                                    Align(
                                      alignment: context.isRtl
                                          ? Alignment.centerLeft
                                          : Alignment.centerRight,
                                      child: CartIcon(
                                          product: item, config: config),
                                    ),
                                  ],
                                ],
                              ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            if (config.enableRating)
                              Expanded(
                                child: SmoothStarRating(
                                    allowHalfRating: true,
                                    starCount: 5,
                                    label: Text(
                                      '${item.ratingCount}',
                                      style: const TextStyle(fontSize: 12),
                                    ),
                                    rating: item.averageRating ?? 0.0,
                                    size: starSize,
                                    color: theme.primaryColor,
                                    borderColor: theme.primaryColor,
                                    spacing: 0.0),
                              ),
                            // if (showCart &&
                            //     !item.isEmptyProduct() &&
                            //     !ServerConfig().isListingType) ...[
                            //   Align(
                            //     alignment: context.isRtl
                            //         ? Alignment.centerLeft
                            //         : Alignment.centerRight,
                            //     child: CartIcon(product: item, config: config),
                            //   ),
                            //   const SizedBox(height: 40)
                            // ]
                          ],
                        )
                      ],
                    ),
                  )
              ],
            ),
            if (config.showHeart && !item.isEmptyProduct())
              Positioned(
                top: 0,
                right: 0,
                child: HeartButton(product: item, size: 18),
              ),
            Positioned.directional(
              start: 8,
              top: 8,
              textDirection: Directionality.of(context),
              child: ProductOnSale(
                product: item,
                config: ProductConfig.empty()..hMargin = 0,
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                decoration: const BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.all(
                    Radius.circular(4),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
